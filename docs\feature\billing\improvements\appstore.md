# App Store Subscription Management

Apple's guidelines are strict: **if you are selling digital content, features, or subscriptions for use *within your app*, you MUST use their In-App Purchase (IAP) system.**

### App Store Server Notifications

The modern and recommended approach is to use **App Store Server Notifications V2**, which sends you a signed JSON Web Token (JWT). This is more secure than the older V1 plaintext JSON notifications.

### The Big Picture: In-App Purchase Flow

1.  **Frontend (iOS App):** Your app uses Apple's `StoreKit` framework to display products and initiate a purchase.
2.  **Apple Processes Payment:** The user authenticates with Face ID/Touch ID. Apple handles the entire payment flow.
3.  **App Receives Transaction:** `StoreKit` on the app receives a successful transaction object, which includes a unique `originalTransactionId`.
4.  **App -> Your Go Backend (Recommended):** Your app sends the `originalTransactionId` to your backend, where you link it to the authenticated user's `UserID`. This step is crucial for identifying which user owns the subscription when the webhook arrives.
5.  **Apple -> Your Go Backend (The Source of Truth):** Asynchronously, Apple sends an **App Store Server Notification** (a signed JWT) to your webhook endpoint. This webhook is your ultimate source of truth for a user's subscription status.
6.  **Your Go Backend:**
    a.  Receives the POST request from Apple.
    b.  **Verifies the JWT signature** using Apple's public keys. This is a critical security step.
    c.  Decodes the JWT payload to get the notification data.
    d.  **Creates or updates the `Subscription` document** in your MongoDB collection based on the `notificationType`.

---

### Step 1: Prerequisites (App Store Connect Setup)

1.  **Create In-App Purchases:** In App Store Connect, under your app, go to "In-App Purchases" and define the products you want to sell (e.g., "Pro Subscription - Monthly").
2.  **Set Up Server Notifications URL:**
    *   Go to `App Store Connect > [Your App] > App Settings > General`.
    *   Scroll down to "App Store Server Notifications".
    *   Enter your production webhook URL (e.g., `https://api.yourdomain.com/webhooks/app-store`).
    *   Choose **Version 2**.
    *   You can also set a URL for the Sandbox environment for testing.
3.  **Generate API Keys:**
    *   Go to `Users and Access > Keys`.
    *   Generate a new API key with "App Manager" access.
    *   Note the **Issuer ID**, the **Key ID**, and **download the private key file (`.p8`)**.

---

### Step 2: Golang Backend Implementation

Let's adapt your code to securely handle V2 notifications and interact with your MongoDB `Subscription` model.

#### Project Setup
You'll need a robust JWT library and the MongoDB Go driver.

```bash
go get github.com/golang-jwt/jwt/v4
go get go.mongodb.org/mongo-driver/mongo
```

#### Your MongoDB `Subscription` Model

Your provided model is perfectly suited for this task. We will map Apple's notification data directly to this structure.

```go
// Your existing Subscription model
type Subscription struct {
	ObjectID primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	// ... core relationships
	UserID    primitive.ObjectID `json:"userId" bson:"userId"`
	PlanID    primitive.ObjectID `json:"planId" bson:"planId"`
	// ... status and lifecycle
	Status SubscriptionStatus `json:"status" bson:"status"`
	// ... provider information
	Provider               PaymentProvider `json:"provider" bson:"provider"`
	ProviderSubscriptionID string          `json:"providerSubscriptionId,omitempty" bson:"providerSubscriptionId,omitempty"` // <-- This will store the originalTransactionId
	// ... dates
	StartDate time.Time `json:"startDate" bson:"startDate"`
	EndDate   time.Time `json:"endDate" bson:"endDate"`
	// ... other fields
	AutoRenew   bool      `json:"autoRenew" bson:"autoRenew"`
	CancelledAt *time.Time `json:"cancelledAt,omitempty" bson:"cancelledAt,omitempty"`
	// ... metadata
	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"`
}
```
**`ProviderSubscriptionID` is the most important field.** We will store Apple's `originalTransactionId` here. It serves as the unique key to find and update a user's subscription record throughout its entire lifecycle.

#### The Webhook Controller (Refined)

The controller is database-agnostic. Its job is to receive the request, pass the signed payload to the service, and handle HTTP responses. This code remains unchanged and is correct.

```go
// controller.go

type AppStoreWebhookRequest struct {
	SignedPayload string `json:"signedPayload"`
}

func (bc *controller) AppStoreWebhook() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var req AppStoreWebhookRequest
		if err := c.Bind(&req); err != nil {
			log.Printf("Failed to bind webhook request: %v", err)
			return c.String(http.StatusBadRequest, "Invalid request body")
		}

		// ... (payload validation)

		err := bc.Service.ProcessAppStoreWebhook(ctx, req.SignedPayload)
		if err != nil {
			log.Printf("Error processing App Store webhook: %v", err)
			return c.String(http.StatusInternalServerError, "Webhook processing failed")
		}

		return c.NoContent(http.StatusOK)
	}
}
```

#### The Webhook Service (The Core Logic)

This is where we implement JWT validation and the specific MongoDB operations. The `verifyAndDecodeJWT` function remains the same. The change is in the individual handler functions.

```go
// service.go

import (
	"context"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

// AppStoreNotification represents the decoded JWT payload.
type AppStoreNotification struct {
	NotificationType string                 `json:"notificationType"`
	Subtype          string                 `json:"subtype"`
	NotificationUUID string                 `json:"notificationUUID"`
	Data             TransactionInfo        `json:"data"`
	Version          string                 `json:"version"`
	SignedDate       int64                  `json:"signedDate"`
	Summary          SubscriptionSummary    `json:"summary"` // Only for summary notifications
	Environment      string                 `json:"environment"` // Custom claim we add after validation
}

type TransactionInfo struct {
	AppAppleID             int64  `json:"appAppleId"`
	BundleID               string `json:"bundleId"`
	BundleVersion          string `json:"bundleVersion"`
	Environment            string `json:"environment"`
	OriginalTransactionID  string `json:"originalTransactionId"`
	ProductID              string `json:"productId"`
	PurchaseDate           int64  `json:"purchaseDate"`
	ExpiresDate            int64  `json:"expiresDate"`
	TransactionID          string `json:"transactionId"`
	Type                   string `json:"type"` // "Auto-Renewable Subscription"
	// ... and many more fields
}

// ... other structs for Summary, etc.

// This is the REFACTORED main router function.
// It now accepts the signedPayload string and performs JWT validation.
func (s *service) ProcessAppStoreWebhook(ctx context.Context, signedPayload string) error {
	// 1. Decode and Verify the JWT (using the function from our previous discussion)
	notification, err := s.verifyAndDecodeJWT(signedPayload)
	if err != nil {
		return errors.New(errors.Service, "JWT verification failed", errors.Validation, err)
	}
    
    // Log for debugging
	log.Printf(
		"Processing notification UUID: %s, Type: %s, OriginalTransactionID: %s",
		notification.NotificationUUID,
		notification.NotificationType,
		notification.Data.OriginalTransactionID,
	)
    
    // TODO: Implement idempotency check using notification.NotificationUUID

	// 2. Route to appropriate handler based on the V2 notification type
	switch notification.NotificationType {
	case "SUBSCRIBED", "DID_RENEW":
        // V1's INITIAL_BUY and DID_RENEW both result in an active subscription.
        // We can combine them into a single handler.
		return s.HandleAppStoreSubscriptionUpsert(ctx, *notification)
	
    case "DID_FAIL_TO_RENEW":
        // This is a billing failure, distinct from a voluntary cancellation.
        return s.HandleAppStoreBillingError(ctx, *notification)

	case "CANCEL":
        // This is a voluntary cancellation by the user.
		return s.HandleAppStoreSubscriptionCancelled(ctx, *notification)

	case "REFUND":
		return s.HandleAppStorePaymentRefunded(ctx, *notification)
        
    case "EXPIRED":
        // The subscription has officially ended.
        return s.HandleAppStoreSubscriptionExpired(ctx, *notification)
        
	default:
		log.Printf("Unhandled App Store notification type: %s", notification.NotificationType)
		return nil
	}
}

// verifyAndDecodeJWT is the most critical security function.
func (s *service) verifyAndDecodeJWT(tokenString string) (*AppStoreNotification, error) {
	// Note: Apple's public key fetching should be cached in production.
	keyFunc := func(token *jwt.Token) (interface{}, error) {
		// Get the x5c header, which contains the certificate chain
		x5c, ok := token.Header["x5c"].([]interface{})
		if !ok || len(x5c) == 0 {
			return nil, errors.New("x5c header missing or empty")
		}

		// The first certificate is the signing cert
		certStr, ok := x5c[0].(string)
		if !ok {
			return nil, errors.New("invalid x5c certificate format")
		}

		certBytes, err := base64.StdEncoding.DecodeString(certStr)
		if err != nil {
			return nil, fmt.Errorf("failed to decode certificate: %w", err)
		}

		cert, err := x509.ParseCertificate(certBytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse certificate: %w", err)
		}

		// TODO: In production, you MUST also verify the certificate chain here
		// to ensure it's signed by Apple's root CA.

		return cert.PublicKey, nil
	}

	token, err := jwt.Parse(tokenString, keyFunc)
	if err != nil {
		return nil, fmt.Errorf("failed to parse JWT: %w", err)
	}

	if !token.Valid {
		return nil, errors.New("token is not valid")
	}

	// Unmarshal the payload into our struct
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("failed to get claims")
	}

	payloadBytes, err := json.Marshal(claims)
	if err != nil {
		return nil, err
	}

	var notification AppStoreNotification
	if err := json.Unmarshal(payloadBytes, &notification); err != nil {
		return nil, fmt.Errorf("failed to unmarshal claims into notification struct: %w", err)
	}

	return &notification, nil
}


// Example handler for a new subscription
// HandleAppStoreSubscriptionUpsert handles both new subscriptions (SUBSCRIBED)
// and renewals (DID_RENEW).
func (s *service) HandleAppStoreSubscriptionUpsert(ctx context.Context, notification AppStoreNotification) error {
	// First, try to find an existing subscription.
	sub, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderAppStore, notification.Data.OriginalTransactionID)

	if err != nil {
        // If it's a "not found" error, this is a new subscription (SUBSCRIBED).
		if errors.IsNotFound(err) {
			// SOLVING THE USER IDENTIFICATION PROBLEM:
			// Your app must have previously sent the originalTransactionId to your backend
			// and you stored a link to the userId. This function looks up that link.
			user, userErr := s.findUserForTransaction(ctx, notification.Data.OriginalTransactionID)
			if userErr != nil {
				return errors.New(errors.Service, "could not link transaction to user", errors.Internal, userErr)
			}
            
            plan, planErr := s.findPlanByProductID(ctx, notification.Data.ProductID)
            if planErr != nil {
                return errors.New(errors.Service, "plan not found for productID", errors.NotFound, planErr)
            }

			// Create a new subscription document
			newSub := &Subscription{
				UserID:                 user.ID,
				PlanID:                 plan.ID,
				Status:                 billing.SubscriptionStatusActive,
				Provider:               billing.PaymentProviderAppStore,
				ProviderSubscriptionID: notification.Data.OriginalTransactionID,
				StartDate:              time.UnixMilli(notification.Data.PurchaseDate),
				EndDate:                time.UnixMilli(notification.Data.ExpiresDate),
				AutoRenew:              true,
				// ... other fields
			}
			return s.repo.Subscriptions().Create(ctx, newSub)
		}
		// If it's another type of error, return it.
		return err
	}

	// If the subscription was found, this is a renewal (DID_RENEW). Update it.
	sub.EndDate = time.UnixMilli(notification.Data.ExpiresDate)
	sub.Status = billing.SubscriptionStatusActive
	return s.repo.Subscriptions().Update(ctx, sub)
}

// HandleAppStoreBillingError handles DID_FAIL_TO_RENEW, putting the user in a grace period.
func (s *service) HandleAppStoreBillingError(ctx context.Context, notification AppStoreNotification) error {
	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderAppStore, notification.Data.OriginalTransactionID)
	if err != nil {
		return err
	}

	subscription.Status = billing.SubscriptionStatusPastDue
	return s.repo.Subscriptions().Update(ctx, subscription)
}

// HandleAppStoreSubscriptionCancelled handles the CANCEL notification.
// This means the user has disabled auto-renew, but their subscription is still active.
func (s *service) HandleAppStoreSubscriptionCancelled(ctx context.Context, notification AppStoreNotification) error {
	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderAppStore, notification.Data.OriginalTransactionID)
	if err != nil {
		return err // Subscription should exist
	}

    // Update the subscription to reflect the user's intent.
    // We do not revoke access here. Access is valid until the EndDate.
	subscription.AutoRenew = false
	subscription.Status = billing.SubscriptionStatusCancelled // A status indicating it will expire
	now := time.Now()
	subscription.CancelledAt = &now

	return s.repo.Subscriptions().Update(ctx, subscription)
}

// HandleSubscriptionExpired updates the status to 'expired'.
func (s *service) HandleSubscriptionExpired(ctx context.Context, n AppStoreNotification) error {
    filter := bson.M{
		"provider":               PaymentProviderAppStore,
		"providerSubscriptionId": n.Data.OriginalTransactionID,
	}

	update := bson.M{
		"$set": bson.M{
			"status":             SubscriptionStatusExpired,
			"cancellationReason": n.Subtype, // Provides context like "VOLUNTARY"
			"updatedAt":          time.Now(),
		},
	}

    return s.SubscriptionRepo.UpdateOne(ctx, filter, update)
}

// HandleAppStorePaymentRefunded handles the REFUND notification, revoking access.
func (s *service) HandleAppStorePaymentRefunded(ctx context.Context, notification AppStoreNotification) error {
	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderAppStore, notification.Data.OriginalTransactionID)
	if err != nil {
		// Even if the subscription is gone, log the refund attempt.
		log.Printf("Received refund for unknown subscription: %s", notification.Data.OriginalTransactionID)
		return err
	}

    // Revoke access immediately.
	subscription.Status = billing.SubscriptionStatusRevoked
	subscription.AutoRenew = false
	subscription.CancellationReason = "refund"

	return s.repo.Subscriptions().Update(ctx, subscription)
}

// HandleAppStoreSubscriptionExpired handles the EXPIRED notification.
// This is the final signal that a subscription's access period has ended.
func (s *service) HandleAppStoreSubscriptionExpired(ctx context.Context, notification AppStoreNotification) error {
	// Find the subscription by its unique provider ID.
	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderAppStore, notification.Data.OriginalTransactionID)
	if err != nil {
		if errors.IsNotFound(err) {
			// This can happen if the subscription was created and expired between webhook checks,
			// or was manually deleted. It's safe to ignore.
			log.Printf("Received EXPIRED notification for a subscription not found in DB: %s", notification.Data.OriginalTransactionID)
			return nil
		}
		// For other errors, we want Apple to retry.
		return err
	}

	// Update the subscription to its final, expired state.
	subscription.Status = billing.SubscriptionStatusExpired
	subscription.AutoRenew = false // Ensure autoRenew is off.

	// The 'subtype' provides valuable analytics on why the subscription expired.
	// e.g., "VOLUNTARY" (user cancelled) or "BILLING_RETRY" (retries failed).
	subscription.CancellationReason = notification.Subtype

	log.Printf(
		"Subscription for %s has officially expired. Reason: %s",
		notification.Data.OriginalTransactionID,
		notification.Subtype,
	)

	return s.repo.Subscriptions().Update(ctx, subscription)
}
```

